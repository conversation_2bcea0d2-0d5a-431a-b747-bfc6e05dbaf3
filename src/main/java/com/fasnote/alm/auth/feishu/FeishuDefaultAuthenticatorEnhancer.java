package com.fasnote.alm.auth.feishu;

import java.util.Map;

import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.polarion.core.util.logging.Logger;
import com.polarion.platform.security.auth.IAuthenticator;

/**
 * 飞书默认认证器增强实现类（回退实现）
 *
 * 当许可证验证不通过时，使用此实现类，保持原样不做增强，只记录日志。
 * 使用 @FallbackImplementation 注解标识为回退实现，支持自动注册。
 */
@FallbackImplementation(
    value = IFeishuAuthenticatorEnhancer.class,
    description = "飞书认证增强默认实现",
    priority = 100
)
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    
    private static final Logger log = Logger.getLogger(FeishuDefaultAuthenticatorEnhancer.class);
    
    @Override
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(Map<String, IAuthenticator> originalAuthenticators) {
        log.info("Feishu license validation failed or not present, using default OAuth2 authenticators");
        
        // 直接返回原始认证器映射，不做任何增强
        return originalAuthenticators;
    }
    
    @Override
    public String getEnhancerType() {
        return "Default OAuth2 Enhancer";
    }
}