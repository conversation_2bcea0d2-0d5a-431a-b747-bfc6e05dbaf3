package com.fasnote.alm.auth.feishu;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceRegistration;

import com.fasnote.alm.plugin.manage.api.IPackageScanProvider;
import com.polarion.core.util.logging.Logger;

public class Activator implements BundleActivator {

	private static final Logger log = Logger.getLogger(Activator.class);
	private static BundleContext context;

	// OSGi 服务注册
	private ServiceRegistration<IPackageScanProvider> packageScanProviderRegistration;

	static BundleContext getContext() {
		return context;
	}

	public void start(BundleContext bundleContext) throws Exception {
		Activator.context = bundleContext;

		log.info("Feishu Authentication Plugin starting...");

		// 注册包扫描提供者服务
		registerPackageScanProvider();

		log.info("Feishu Authentication Plugin started successfully");
		log.info("飞书插件实现类将通过 OSGi 服务自动扫描注册");
	}

	public void stop(BundleContext bundleContext) throws Exception {
		log.info("Feishu Authentication Plugin stopping...");

		// 注销 OSGi 服务
		if (packageScanProviderRegistration != null) {
			try {
				packageScanProviderRegistration.unregister();
				log.info("FeishuPackageScanProvider OSGi 服务已注销");
			} catch (Exception e) {
				log.warn("注销 FeishuPackageScanProvider OSGi 服务时发生异常", e);
			}
			packageScanProviderRegistration = null;
		}

		Activator.context = null;
		log.info("Feishu Authentication Plugin stopped");
	}

	/**
	 * 注册包扫描提供者为 OSGi 服务
	 */
	private void registerPackageScanProvider() {
		try {
			log.info("注册 FeishuPackageScanProvider 为 OSGi 服务...");

			// 创建包扫描提供者实例
			IPackageScanProvider packageScanProvider = new FeishuPackageScanProvider();

			// 注册为 OSGi 服务
			packageScanProviderRegistration = context.registerService(
				IPackageScanProvider.class,
				packageScanProvider,
				null
			);

			log.info("FeishuPackageScanProvider OSGi 服务注册成功");
			log.info("扫描包路径: " + java.util.Arrays.toString(packageScanProvider.getScanPackages()));

		} catch (Exception e) {
			log.error("注册 FeishuPackageScanProvider OSGi 服务失败", e);
		}
	}

}
