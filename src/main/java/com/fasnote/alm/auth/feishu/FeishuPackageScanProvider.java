package com.fasnote.alm.auth.feishu;

import com.fasnote.alm.plugin.manage.api.IPackageScanProvider;

/**
 * 飞书插件包扫描提供者
 * 
 * 通过 OSGi 服务机制声明飞书插件需要扫描的包路径
 */
public class FeishuPackageScanProvider implements IPackageScanProvider {
    
    @Override
    public String[] getScanPackages() {
        return new String[] {
            "com.fasnote.alm.auth.feishu"
        };
    }
    
    @Override
    public String getPluginId() {
        return "feishu-auth-plugin";
    }
    
    @Override
    public String getName() {
        return "Feishu Authentication Package Scanner";
    }
    
    @Override
    public int getPriority() {
        return 50; // 较高优先级
    }
}
