package com.fasnote.alm.auth.feishu;

import com.fasnote.alm.plugin.manage.injection.LicenseBusinessRegistry;
import com.fasnote.alm.plugin.manage.injection.PackageScanManager;
import com.fasnote.alm.plugin.manage.api.ILicenseBusinessRegistry;
import com.fasnote.alm.plugin.manage.api.IPackageScanProvider;
import com.polarion.core.util.logging.Logger;

/**
 * 飞书注册验证器
 * 
 * 用于验证飞书插件的实现类是否能够通过注解正确扫描和注册
 */
public class FeishuRegistrationVerifier {
    
    private static final Logger logger = Logger.getLogger(FeishuRegistrationVerifier.class);
    
    /**
     * 验证飞书插件的注解扫描注册
     */
    public static void verifyFeishuRegistration() {
        logger.info("=== 开始验证飞书插件注解扫描注册 ===");

        try {
            // 验证包扫描提供者
            verifyPackageScanProvider();

            // 创建包扫描管理器（ServiceLoader 模式）
            PackageScanManager packageScanManager = new PackageScanManager(null);
            packageScanManager.start();

            // 获取扫描包路径
            String[] scanPackages = packageScanManager.getAllScanPackages();
            logger.info("从包扫描管理器获取的扫描包路径: " + java.util.Arrays.toString(scanPackages));

            // 创建注册器实例
            ILicenseBusinessRegistry registry = new LicenseBusinessRegistry();

            // 扫描包路径
            registry.scanAndRegister(scanPackages);

            // 获取注册统计
            ILicenseBusinessRegistry.RegistrationStats stats = registry.getRegistrationStats();
            logger.info("扫描结果统计:");
            logger.info("- 许可证实现数量: " + stats.getLicenseImplementationCount());
            logger.info("- 回退实现数量: " + stats.getFallbackImplementationCount());
            logger.info("- 总注册数量: " + stats.getTotalCount());

            // 检查具体的服务接口注册情况
            boolean hasLicenseImpl = registry.hasLicenseImplementation(IFeishuAuthenticatorEnhancer.class);
            boolean hasFallbackImpl = registry.hasFallbackImplementation(IFeishuAuthenticatorEnhancer.class);

            logger.info("IFeishuAuthenticatorEnhancer 注册情况:");
            logger.info("- 许可证实现: " + hasLicenseImpl);
            logger.info("- 回退实现: " + hasFallbackImpl);

            // 显示已注册的服务接口
            logger.info("已注册的服务接口: " + registry.getRegisteredServiceInterfaces());

            // 验证结果
            if (hasLicenseImpl && hasFallbackImpl) {
                logger.info("✅ 飞书插件注解扫描注册验证成功！");
            } else {
                logger.warn("❌ 飞书插件注解扫描注册验证失败！");
                if (!hasLicenseImpl) {
                    logger.warn("  - 缺少许可证实现注册");
                }
                if (!hasFallbackImpl) {
                    logger.warn("  - 缺少回退实现注册");
                }
            }

            // 停止包扫描管理器
            packageScanManager.stop();

        } catch (Exception e) {
            logger.error("验证飞书插件注解扫描注册时发生异常", e);
        }

        logger.info("=== 飞书插件注解扫描注册验证完成 ===");
    }

    /**
     * 验证包扫描提供者
     */
    private static void verifyPackageScanProvider() {
        logger.info("--- 验证包扫描提供者 ---");

        try {
            IPackageScanProvider provider = new FeishuPackageScanProvider();
            logger.info("包扫描提供者信息:");
            logger.info("- 插件ID: " + provider.getPluginId());
            logger.info("- 名称: " + provider.getName());
            logger.info("- 优先级: " + provider.getPriority());
            logger.info("- 扫描包路径: " + java.util.Arrays.toString(provider.getScanPackages()));

        } catch (Exception e) {
            logger.error("验证包扫描提供者时发生异常", e);
        }
    }
    
    /**
     * 主方法，用于独立测试
     */
    public static void main(String[] args) {
        verifyFeishuRegistration();
    }
}
