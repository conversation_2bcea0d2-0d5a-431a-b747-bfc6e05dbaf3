package com.fasnote.alm.plugin.manage.facade;

import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;

/**
 * 配置管理门面 职责：配置访问接口
 *
 * 功能： - 配置获取和设置 - 类型转换 - 委托给LicenseConfiguration
 */
public class ConfigurationFacade {

	private final LicenseConfiguration configuration;

	public ConfigurationFacade() {
		this.configuration = LicenseConfiguration.getInstance();
	}

	/**
	 * 构造函数（依赖注入）
	 */
	public ConfigurationFacade(LicenseConfiguration configuration) {
		this.configuration = configuration;
	}

	/**
	 * 获取布尔配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 布尔值
	 */
	public boolean getBooleanConfig(String key, boolean defaultValue) {
		return configuration.getBooleanConfig(key, defaultValue);
	}

	/**
	 * 获取配置管理器
	 */
	public LicenseConfiguration getConfiguration() {
		return configuration;
	}

	/**
	 * 获取配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 配置值
	 */
	public String getConfigValue(String key, String defaultValue) {
		return configuration.getConfigValue(key, defaultValue);
	}

	/**
	 * 获取整数配置值
	 *
	 * @param key          配置键
	 * @param defaultValue 默认值
	 * @return 整数值
	 */
	public int getIntConfig(String key, int defaultValue) {
		return configuration.getIntConfig(key, defaultValue);
	}

	/**
	 * 重新加载配置
	 */
	public void reloadConfiguration() {
		configuration.reloadConfiguration();
	}

	/**
	 * 设置配置值
	 *
	 * @param key   配置键
	 * @param value 配置值
	 */
	public void setConfigValue(String key, String value) {
		configuration.setConfigValue(key, value);
	}
}
