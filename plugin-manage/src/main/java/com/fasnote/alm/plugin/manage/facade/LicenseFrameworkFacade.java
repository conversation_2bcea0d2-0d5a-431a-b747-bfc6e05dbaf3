package com.fasnote.alm.plugin.manage.facade;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasnote.alm.plugin.manage.bootstrap.LicenseFrameworkBootstrap;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.model.LicenseInfo;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.monitor.FrameworkMonitor;
import com.fasnote.alm.plugin.manage.registry.LicenseServiceRegistry;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证框架统一门面 职责：提供统一的API入口
 *
 * 功能： - 协调各个子系统 - 提供简化的API - 保持向后兼容性
 */
public class LicenseFrameworkFacade {

	// 单例实例
	private static volatile LicenseFrameworkFacade instance;
	/**
	 * 获取单例实例
	 */
	public static LicenseFrameworkFacade getInstance() {
		if (instance == null) {
			synchronized (LicenseFrameworkFacade.class) {
				if (instance == null) {
					instance = new LicenseFrameworkFacade();
				}
			}
		}
		return instance;
	}
	// 子系统门面
	private final LicenseFrameworkBootstrap bootstrap;
	private final LicenseServiceRegistry serviceRegistry;
	private final LicenseManagementFacade licenseManagement;
	private final ConfigurationFacade configuration;

	private final SecurityFacade security;

	private final FrameworkMonitor monitor;

	/**
	 * 私有构造函数
	 */
	private LicenseFrameworkFacade() {
		// 创建依赖组件
		SecurityValidator securityValidator = new SecurityValidator();

		// 创建 LicenseManager 实例
		LicenseManager licenseManager = new LicenseManager(securityValidator);

		// 创建 LicenseFrameworkBootstrap 实例
		this.bootstrap = new LicenseFrameworkBootstrap(licenseManager);

		// 确保框架已初始化
		if (!bootstrap.isInitialized()) {
			bootstrap.initialize();
		}

		// 初始化各个门面（使用依赖注入，共享同一套组件）
		this.serviceRegistry = new LicenseServiceRegistry();
		this.licenseManagement = new LicenseManagementFacade(licenseManager);
		this.configuration = new ConfigurationFacade(bootstrap.getConfiguration());
		this.security = new SecurityFacade(securityValidator);
		this.monitor = new FrameworkMonitor(licenseManager, bootstrap.getConfiguration());
	}

	// ==================== 服务注册相关API ====================

	/**
	 * 检查模块安装状态
	 */
	public void checkModuleInstallationStatus() {
		serviceRegistry.checkModuleInstallationStatus();
	}

	/**
	 * 创建并注入依赖的组件实例
	 */
	public <T> T createComponent(Class<T> componentClass) {
		return serviceRegistry.createComponent(componentClass);
	}

	/**
	 * 获取所有插件许可证信息
	 */
	public List<LicenseInfo> getAllPluginLicenses() {
		return licenseManagement.getAllPluginLicenses();
	}

	/**
	 * 获取布尔配置值
	 */
	public boolean getBooleanConfig(String key, boolean defaultValue) {
		return configuration.getBooleanConfig(key, defaultValue);
	}

	/**
	 * 获取配置值
	 */
	public String getConfigValue(String key, String defaultValue) {
		return configuration.getConfigValue(key, defaultValue);
	}

	/**
	 * 获取依赖注入器
	 */
	public Object getDependencyInjector() {
		return serviceRegistry.getDependencyInjector();
	}

	/**
	 * 获取框架描述
	 */
	public String getDescription() {
		return monitor.getDescription();
	}

	/**
	 * 获取框架统计信息
	 */
	public Map<String, Object> getFrameworkStatistics() {
		return monitor.getFrameworkStatistics();
	}

	// ==================== 许可证管理相关API ====================

	/**
	 * 获取整数配置值
	 */
	public int getIntConfig(String key, int defaultValue) {
		return configuration.getIntConfig(key, defaultValue);
	}

	/**
	 * 获取许可证管理器
	 */
	public LicenseManager getLicenseManager() {
		return licenseManagement.getLicenseManager();
	}

	/**
	 * 获取插件许可证状态
	 */
	public String getLicenseStatus(String pluginId) {
		return licenseManagement.getLicenseStatus(pluginId);
	}

	/**
	 * 获取当前机器码
	 */
	public String getMachineCode() {
		return security.getMachineCode();
	}

	/**
	 * 获取插件许可证信息
	 */
	public LicenseInfo getPluginLicenseInfo(String pluginId) {
		return licenseManagement.getPluginLicenseInfo(pluginId);
	}

	/**
	 * 获取所有已注册的插件ID
	 */
	public Set<String> getRegisteredPluginIds() {
		return licenseManagement.getRegisteredPluginIds();
	}

	/**
	 * 获取服务（按类型）
	 */
	public <T> T getService(Class<T> serviceClass) {
		return serviceRegistry.getService(serviceClass);
	}

	/**
	 * 获取命名服务
	 */
	public <T> T getService(Class<T> serviceClass, String name) {
		return serviceRegistry.getService(serviceClass, name);
	}

	/**
	 * 获取服务
	 */
	public <T> T getService(String serviceName) {
		return serviceRegistry.getService(serviceName);
	}

	/**
	 * 获取框架版本
	 */
	public String getVersion() {
		return monitor.getVersion();
	}

	// ==================== 配置管理相关API ====================

	/**
	 * 健康检查
	 */
	public ValidationResult healthCheck() {
		return monitor.healthCheck();
	}

	/**
	 * 检查功能是否可用
	 */
	public boolean isFeatureEnabled(String featureName) {
		return licenseManagement.isFeatureEnabled(featureName);
	}

	/**
	 * 刷新许可证状态
	 */
	public void refreshLicenseStatus() {
		licenseManagement.refreshLicenseStatus();
	}

	/**
	 * 注册插件许可证
	 */
	public ValidationResult registerPluginLicense(String pluginId, byte[] licenseData) {
		return licenseManagement.registerPluginLicense(pluginId, licenseData);
	}

	/**
	 * 注册服务（按类型）
	 */
	public <T> void registerService(Class<T> serviceClass, T serviceInstance) {
		serviceRegistry.registerService(serviceClass, serviceInstance);
	}

	// ==================== 安全相关API ====================

	/**
	 * 注册服务
	 */
	public <T> void registerService(String serviceName, T serviceInstance) {
		serviceRegistry.registerService(serviceName, serviceInstance);
	}

	// ==================== 系统管理相关API ====================

	/**
	 * 重新加载配置
	 */
	public void reloadConfiguration() {
		configuration.reloadConfiguration();
	}

	/**
	 * 移除插件许可证
	 */
	public void removePluginLicense(String pluginId) {
		licenseManagement.removePluginLicense(pluginId);
	}

	/**
	 * 设置配置值
	 */
	public void setConfigValue(String key, String value) {
		configuration.setConfigValue(key, value);
	}

	/**
	 * 关闭框架
	 */
	public void shutdown() {
		bootstrap.shutdown();
	}

	/**
	 * 更新插件许可证
	 */
	public boolean updatePluginLicense(String pluginId, String licenseContent) {
		return licenseManagement.updatePluginLicense(pluginId, licenseContent);
	}

	/**
	 * 验证插件许可证
	 */
	public ValidationResult validatePluginLicense(String pluginId) {
		return licenseManagement.validatePluginLicense(pluginId);
	}
}
