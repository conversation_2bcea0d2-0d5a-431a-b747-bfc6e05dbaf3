package com.fasnote.alm.plugin.manage.osgi;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceReference;
import org.osgi.framework.ServiceRegistration;
import org.osgi.util.tracker.ServiceTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.facade.LicenseFrameworkFacade;

/**
 * OSGi许可证管理激活器 负责在OSGi环境中启动和管理许可证验证框架
 *
 * 功能： 1. 启动许可证管理框架 2. 注册OSGi服务 3. 跟踪和注入OSGi服务 4. 管理框架生命周期
 */
public class OSGiLicenseActivator implements BundleActivator {

	private static final Logger logger = LoggerFactory.getLogger(OSGiLicenseActivator.class);

	private BundleContext bundleContext;
	private LicenseFrameworkFacade framework;

	// 服务注册
	private ServiceRegistration<LicenseFrameworkFacade> frameworkRegistration;

	// 服务跟踪器
	private ServiceTracker<Object, Object> serviceTracker;

	// Bundle监听器
	private LicenseBundleListener bundleListener;

	/**
	 * 获取Bundle上下文
	 */
	public BundleContext getBundleContext() {
		return bundleContext;
	}

	/**
	 * 获取许可证管理框架
	 */
	public LicenseFrameworkFacade getFramework() {
		return framework;
	}

	/**
	 * 处理服务添加事件
	 */
	private void handleServiceAdded(ServiceReference<Object> reference, Object service) {
		try {
			String[] objectClasses = (String[]) reference.getProperty("objectClass");
			if (objectClasses != null && objectClasses.length > 0) {
				String serviceClassName = objectClasses[0];

				logger.debug("检测到新的OSGi服务: {}", serviceClassName);

				// 检查是否是我们感兴趣的服务
				if (isInterestedService(serviceClassName)) {
					registerServiceToFramework(serviceClassName, service);
				}
			}

		} catch (Exception e) {
			logger.error("处理服务添加事件失败", e);
		}
	}

	/**
	 * 处理服务移除事件
	 */
	private void handleServiceRemoved(ServiceReference<Object> reference, Object service) {
		try {
			String[] objectClasses = (String[]) reference.getProperty("objectClass");
			if (objectClasses != null && objectClasses.length > 0) {
				String serviceClassName = objectClasses[0];

				logger.debug("检测到OSGi服务移除: {}", serviceClassName);

				// 从框架中移除服务引用
				unregisterServiceFromFramework(serviceClassName, service);
			}

		} catch (Exception e) {
			logger.error("处理服务移除事件失败", e);
		}
	}

	/**
	 * 初始化许可证管理框架
	 */
	private void initializeFramework() throws Exception {
		logger.info("初始化许可证管理框架");

		// 暂时跳过旧的框架门面，因为它依赖于已废弃的单例模式
		// 新的依赖注入架构通过 OSGiLicenseFrameworkInitializer 来管理
		logger.info("使用新的依赖注入架构，跳过旧的框架门面");

		// 设置framework为null，表示使用新架构
		framework = null;

		logger.info("许可证管理框架初始化完成（使用新架构）");
	}

	/**
	 * 检查是否是感兴趣的服务
	 */
	private boolean isInterestedService(String serviceClassName) {
		// 定义我们感兴趣的服务类型
		return serviceClassName.startsWith("com.polarion.") || serviceClassName.startsWith("com.fasnote.")
				|| serviceClassName.contains("LicensePlugin") || serviceClassName.contains("SecurityService")
				|| serviceClassName.contains("ConfigurationService");
	}

	/**
	 * 注册默认服务
	 */
	private void registerDefaultPlugins() {
		logger.info("注册默认服务");

		try {
			// 这里可以注册一些默认的服务
			// 例如：从配置文件中读取服务列表并注册

			// 示例：注册一些默认服务到框架
			// framework.registerService("DefaultService", new DefaultServiceImpl());

			logger.info("默认服务注册完成");

		} catch (Exception e) {
			logger.error("注册默认服务失败", e);
		}
	}

	/**
	 * 注册框架服务
	 */
	private void registerFrameworkService() {
		logger.info("跳过注册旧的框架服务");

		// 新的依赖注入架构不需要注册旧的框架门面服务
		// 服务通过DI框架自动管理
		frameworkRegistration = null;

		logger.info("使用新的依赖注入架构，无需注册旧框架服务");
	}

	/**
	 * 将OSGi服务注册到框架
	 */
	private void registerServiceToFramework(String serviceClassName, Object service) {
		try {
			logger.debug("注册OSGi服务到框架: {}", serviceClassName);

			// TODO: 集成全局DI框架
			// 暂时注释掉依赖注入相关代码，因为依赖项不可用
			/*
			 * if (DI.isAvailable()) { Class<?> serviceClass = service.getClass();
			 *
			 * // 注册服务的所有接口 for (Class<?> interfaceClass : serviceClass.getInterfaces()) {
			 * if (interfaceClass.isInstance(service)) {
			 *
			 * @SuppressWarnings("unchecked") Class<Object> objClass = (Class<Object>)
			 * interfaceClass; injector.registerSingleton(objClass, service);
			 * logger.debug("注册OSGi服务接口: {}", interfaceClass.getName()); } }
			 *
			 * // 也注册服务本身的类
			 *
			 * @SuppressWarnings("unchecked") Class<Object> objServiceClass =
			 * (Class<Object>) serviceClass; injector.registerSingleton(objServiceClass,
			 * service);
			 *
			 * logger.info("OSGi服务注册成功: {}", serviceClassName); }
			 */

			logger.debug("OSGi服务处理完成: {}", serviceClassName);

		} catch (Exception e) {
			logger.error("注册OSGi服务到框架失败: {}", serviceClassName, e);
		}
	}

	@Override
	public void start(BundleContext context) throws Exception {
		this.bundleContext = context;

		logger.info("启动OSGi许可证管理激活器");

		try {
			// 1. 初始化许可证管理框架
			initializeFramework();

			// 2. 注册框架服务
			registerFrameworkService();

			// 3. 启动Bundle监听器
			startBundleListener();

			// 4. 启动服务跟踪器
			startServiceTracker();

			// 5. 注册默认插件（如果有）
			registerDefaultPlugins();

			logger.info("OSGi许可证管理激活器启动完成");

		} catch (Exception e) {
			logger.error("OSGi许可证管理激活器启动失败", e);
			throw e;
		}
	}

	/**
	 * 启动Bundle监听器
	 */
	private void startBundleListener() {
		logger.info("启动Bundle监听器");

		try {
			// 获取许可证管理器
			LicenseManager licenseManager = framework.getLicenseManager();

			// 创建Bundle监听器
			bundleListener = new LicenseBundleListener(licenseManager);

			// 注册Bundle监听器
			bundleContext.addBundleListener(bundleListener);

			logger.info("Bundle监听器启动完成");

		} catch (Exception e) {
			logger.error("启动Bundle监听器失败", e);
			throw new RuntimeException("启动Bundle监听器失败", e);
		}
	}

	/**
	 * 启动服务跟踪器
	 */
	private void startServiceTracker() {
		logger.info("启动OSGi服务跟踪器");

		// 创建服务跟踪器来监控OSGi服务
		serviceTracker = new ServiceTracker<>(bundleContext, Object.class, null) {

			@Override
			public Object addingService(ServiceReference<Object> reference) {
				Object service = super.addingService(reference);
				handleServiceAdded(reference, service);
				return service;
			}

			@Override
			public void removedService(ServiceReference<Object> reference, Object service) {
				handleServiceRemoved(reference, service);
				super.removedService(reference, service);
			}
		};

		serviceTracker.open();
		logger.info("OSGi服务跟踪器启动完成");
	}

	@Override
	public void stop(BundleContext context) throws Exception {
		logger.info("停止OSGi许可证管理激活器");

		try {
			// 1. 停止Bundle监听器
			if (bundleListener != null) {
				bundleContext.removeBundleListener(bundleListener);
				bundleListener.cleanup();
				bundleListener = null;
			}

			// 2. 停止服务跟踪器
			if (serviceTracker != null) {
				serviceTracker.close();
				serviceTracker = null;
			}

			// 3. 注销框架服务
			if (frameworkRegistration != null) {
				frameworkRegistration.unregister();
				frameworkRegistration = null;
			}

			// 4. 关闭许可证管理框架
			if (framework != null) {
				framework.shutdown();
				framework = null;
			}

			logger.info("OSGi许可证管理激活器停止完成");

		} catch (Exception e) {
			logger.error("OSGi许可证管理激活器停止失败", e);
			throw e;
		}
	}

	/**
	 * 从框架中注销OSGi服务
	 */
	private void unregisterServiceFromFramework(String serviceClassName, Object service) {
		logger.debug("从框架中注销OSGi服务: {}", serviceClassName);
		// 注意：当前的DependencyInjector没有提供注销单个服务的方法
		// 在实际应用中可能需要扩展DependencyInjector来支持服务注销
	}
}
