package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 许可证实现注解 标记需要许可证验证的类
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface LicenseImplementation {

	/**
	 * 功能描述
	 */
	String description() default "";

	/**
	 * 许可证级别
	 */
	String level() default "STANDARD";

	/**
	 * 是否为高级功能
	 */
	boolean premium() default false;
}