package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 回退实现注解
 *
 * 标记一个类为某个服务接口的回退实现。 当许可证验证失败时，系统会使用此实现作为降级服务。
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface FallbackImplementation {

	/**
	 * 生命周期枚举
	 */
	enum Scope {
		SINGLETON, // 单例
		PROTOTYPE // 原型（每次创建新实例）
	}

	/**
	 * 功能描述
	 */
	String description() default "";

	/**
	 * 服务名称（可选） 支持一个接口多个实现的场景
	 */
	String name() default "";

	/**
	 * 优先级 数字越小优先级越高，默认为100（低优先级）
	 */
	int priority() default 100;

	/**
	 * 生命周期
	 */
	Scope scope() default Scope.SINGLETON;

	/**
	 * 服务接口类型 必须指定此回退实现对应的服务接口
	 */
	Class<?> value();
}
