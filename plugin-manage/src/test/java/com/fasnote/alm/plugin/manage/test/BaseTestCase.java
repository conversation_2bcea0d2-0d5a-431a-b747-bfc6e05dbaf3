package com.fasnote.alm.plugin.manage.test;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Properties;

import org.junit.Before;
import org.junit.Rule;
import org.junit.rules.TemporaryFolder;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;

import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.audit.AuditLogger;
import com.fasnote.alm.plugin.manage.config.LicenseConfiguration;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 测试基类 提供通用的测试工具方法和Mock对象
 */
public abstract class BaseTestCase {

	// 测试常量
	protected static final String TEST_PLUGIN_ID = "com.test.plugin";

	protected static final String TEST_PRODUCT_NAME = "Test Product";

	protected static final String TEST_LICENSE_TYPE = "COMMERCIAL";

	protected static final int TEST_MAX_USERS = 100;

	@Rule
	public TemporaryFolder tempFolder = new TemporaryFolder();

	@Mock
	protected BundleContext mockBundleContext;

	@Mock
	protected Bundle mockBundle;
	@Mock
	protected AuditLogger mockAuditLogger;
	@Mock
	protected LicenseConfiguration mockLicenseConfiguration;

	@Mock
	protected RSALicenseEncryption mockRSALicenseEncryption;
	// 测试用的RSA密钥对
	protected KeyPair testKeyPair;
	protected PublicKey testPublicKey;
	protected PrivateKey testPrivateKey;

	/**
	 * 创建测试用的加密许可证数据
	 */
	protected byte[] createTestEncryptedLicenseData(String pluginId, boolean expired) throws Exception {
		String licenseJson = createTestLicenseJson(pluginId, expired);
		return licenseJson.getBytes("UTF-8");
	}

	/**
	 * 创建测试用的InputStream
	 */
	protected InputStream createTestInputStream(String content) {
		return new ByteArrayInputStream(content.getBytes());
	}

	/**
	 * 创建测试用的许可证JSON数据
	 */
	protected String createTestLicenseJson(String pluginId, boolean expired) {
		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime expiryTime = expired ? currentTime.minusDays(1) : currentTime.plusYears(1);

		return String.format(
				"{\n" + "  \"pluginId\": \"%s\",\n" + "  \"productName\": \"%s\",\n" + "  \"licenseType\": \"%s\",\n"
						+ "  \"expiryDate\": \"%s\",\n" + "  \"maxUsers\": %d,\n"
						+ "  \"machineCode\": \"TEST_MACHINE_CODE\",\n"
						+ "  \"features\": {\"FEATURE_A\": true, \"FEATURE_B\": true},\n"
						+ "  \"signature\": \"TEST_SIGNATURE\"\n" + "}",
				pluginId, TEST_PRODUCT_NAME, TEST_LICENSE_TYPE,
				expiryTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), TEST_MAX_USERS);
	}

	/**
	 * 创建测试用的PluginLicense对象
	 */
	protected PluginLicense createTestPluginLicense() {
		return createTestPluginLicense(TEST_PLUGIN_ID, false);
	}

	/**
	 * 创建测试用的PluginLicense对象
	 *
	 * @param pluginId 插件ID
	 * @param expired  是否过期
	 */
	protected PluginLicense createTestPluginLicense(String pluginId, boolean expired) {
		String licenseJson = createTestLicenseJson(pluginId, expired);
		PluginLicense license = new PluginLicense(pluginId, licenseJson);

		// 设置过期时间
		LocalDateTime currentTime = LocalDateTime.now();
		if (expired) {
			license.setExpiryDate(currentTime.minusDays(1)); // 昨天过期
		} else {
			license.setExpiryDate(currentTime.plusYears(1)); // 一年后过期
		}

		// 设置机器码（测试用）
		license.setMachineCode("TEST_MACHINE_CODE");

		return license;
	}

	/**
	 * 创建测试用的Properties配置
	 */
	protected Properties createTestProperties() {
		Properties props = new Properties();
		props.setProperty("license.directory", tempFolder.getRoot().getAbsolutePath());
		props.setProperty("license.encryption.enabled", "true");
		props.setProperty("license.machine.binding.enabled", "true");
		props.setProperty("license.signature.validation.enabled", "true");
		props.setProperty("license.cache.size", "100");
		props.setProperty("license.cache.ttl", "3600");
		props.setProperty("license.audit.enabled", "true");
		props.setProperty("license.check.interval", "300");
		return props;
	}

	/**
	 * 生成测试用RSA密钥对
	 */
	private void generateTestKeyPair() throws Exception {
		KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
		keyGen.initialize(2048);
		testKeyPair = keyGen.generateKeyPair();
		testPublicKey = testKeyPair.getPublic();
		testPrivateKey = testKeyPair.getPrivate();
	}

	/**
	 * 获取测试用的私钥字符串（Base64编码）
	 */
	protected String getTestPrivateKeyString() {
		return Base64.getEncoder().encodeToString(testPrivateKey.getEncoded());
	}

	/**
	 * 获取测试用的公钥字符串（Base64编码）
	 */
	protected String getTestPublicKeyString() {
		return Base64.getEncoder().encodeToString(testPublicKey.getEncoded());
	}

	/**
	 * 重置所有Mock对象
	 */
	protected void resetAllMocks() {
		reset(mockBundleContext, mockBundle, mockAuditLogger, mockLicenseConfiguration, mockRSALicenseEncryption);
		setupDefaultMockBehavior();
	}

	@Before
	public void setUpBase() throws Exception {
		MockitoAnnotations.openMocks(this);

		// 生成测试用RSA密钥对
		generateTestKeyPair();

		// 设置默认的Mock行为
		setupDefaultMockBehavior();
	}

	/**
	 * 设置默认的Mock行为
	 */
	protected void setupDefaultMockBehavior() {
		// 设置Bundle Context默认行为
		when(mockBundleContext.getBundles()).thenReturn(new Bundle[] { mockBundle });
		when(mockBundle.getState()).thenReturn(Bundle.ACTIVE);
		when(mockBundle.getSymbolicName()).thenReturn(TEST_PLUGIN_ID);

		// 设置LicenseConfiguration默认行为
		when(mockLicenseConfiguration.getLicenseDirectory()).thenReturn(tempFolder.getRoot().getAbsolutePath());
		when(mockLicenseConfiguration.isEncryptionEnabled()).thenReturn(true);
		when(mockLicenseConfiguration.isMachineBindingEnabled()).thenReturn(true);
		when(mockLicenseConfiguration.isSignatureValidationEnabled()).thenReturn(true);
		when(mockLicenseConfiguration.getCacheSize()).thenReturn(100);
		when(mockLicenseConfiguration.getCacheTTL()).thenReturn(3600L);

		// 设置AuditLogger默认行为（静默模式）
		doNothing().when(mockAuditLogger).info(anyString());
		doNothing().when(mockAuditLogger).warn(anyString());
		doNothing().when(mockAuditLogger).error(anyString());
		doNothing().when(mockAuditLogger).debug(anyString());
	}

	/**
	 * 验证Mock对象的交互
	 */
	protected void verifyNoMoreInteractionsOnMocks() {
		verifyNoMoreInteractions(mockBundleContext, mockBundle, mockAuditLogger, mockLicenseConfiguration,
				mockRSALicenseEncryption);
	}
}
