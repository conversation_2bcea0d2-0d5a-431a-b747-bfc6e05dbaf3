package com.fasnote.alm.injection.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 服务注解
 * 标记一个类为服务实现类
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Service {
    
    /**
     * 服务名称
     */
    String value() default "";
    
    /**
     * 服务接口类型（如果不指定，将自动推断）
     */
    Class<?>[] interfaces() default {};
}